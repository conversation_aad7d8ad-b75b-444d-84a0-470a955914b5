/**
 * 标签模板配置文件
 * 直接存放原有格式的模板数据
 */

const defaultTemplates = [

  {
    "TemplateName": "存储标签1",
    "Width": 50,
    "Height": 30,
    "Rotate": 1,
    "Copies": 1,
    "Density": 3,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 30,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_001.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_001.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 13,
        "Y": 3,
        "Width": 33,
        "Height": 4,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 17,
        "Y": 12,
        "Width": 30,
        "Height": 4,
        "Content": "操作人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 13,
        "Y": 21,
        "Width": 33,
        "Height": 4,
        "Content": "日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "5",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      }
    ]
  },
  {
    "TemplateName": "食材管控标签",
    "Width": 50,
    "Height": 30,
    "Rotate": 0,
    "Copies": 1,
    "Density": 2,
    "HorizontalNum": 0,
    "VerticalNum": 0,
    "PaperType": 1,
    "Gap": 2,
    "Speed": 25,
    "FirstCut": 1,
    "CutType": 1,
    "DeviceSn": "T0145B",
    "ImageWidth": 50,
    "ImageHeight": 30,
    "PreviewPath": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_011.png",
    "DrawObjects": [
      {
        "AntiColor": false,
        "X": 0,
        "Y": 0,
        "Width": 50,
        "Height": 30,
        "Content": "https://welshine-official-pro01.oss-cn-guangzhou.aliyuncs.com/static/50x30_011.png",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "4",
        "Format": "IMAGE",
        "Orientation": 0
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 4.7,
        "Width": 33,
        "Height": 4,
        "Content": "品名",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 8.7,
        "Width": 33,
        "Height": 4,
        "Content": "生产日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE",
        "InputMax": 8
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 12.7,
        "Width": 33,
        "Height": 4,
        "Content": "入库日期",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 16.7,
        "Width": 33,
        "Height": 4,
        "Content": "有效期至",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "DATE"
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 20.7,
        "Width": 33,
        "Height": 4,
        "Content": "贮存条件",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "CHECK",
        "InputRegex": "|常温 |冷藏 |冷冻"
      },
      {
        "AntiColor": false,
        "X": 14,
        "Y": 24.7,
        "Width": 33,
        "Height": 4,
        "Content": "填写人",
        "FontName": "HarmonyOS Sans SC",
        "FontStyle": 2,
        "FontSize": "3",
        "Format": "TEXT",
        "Orientation": 0,
        "IsInput": true,
        "InputFormat": "TEXT",
        "InputMax": 8
      },
    ]
  }
];

/**
 * 获取默认模板列表
 * @returns {Array} 模板数组的深拷贝
 */
function getDefaultTemplates() {
  return JSON.parse(JSON.stringify(defaultTemplates));
}

module.exports = {
  defaultTemplates,
  getDefaultTemplates
};
